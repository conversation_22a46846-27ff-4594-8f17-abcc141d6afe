#!/usr/bin/env python3
"""
Ella测试用例生成器
用于根据命令和期望文本生成标准化的Ella测试用例文件
"""
import os
import re
from typing import List, Union

from core.logger import log

# 获取当前相对目录"
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# test_ella目录
test_ella_dir = os.path.join(current_dir, "testcases", "test_ella")



class EllaTestGenerator:
    """Ella测试用例生成器"""
    
    def __init__(self, base_dir: str = test_ella_dir):
        """
        初始化生成器
        
        Args:
            base_dir: 测试用例基础目录
        """
        self.base_dir = base_dir
        
    def sanitize_filename(self, command: str) -> str:
        """
        将命令转换为合法的文件名
        
        Args:
            command: 原始命令
            
        Returns:
            str: 合法的文件名
        """
        # 移除特殊字符，替换空格为下划线
        filename = re.sub(r'[^\w\s-]', '', command.lower())
        filename = re.sub(r'[-\s]+', '_', filename)
        return f"test_{filename}"
    
    def sanitize_class_name(self, command: str) -> str:
        """
        将命令转换为合法的类名
        
        Args:
            command: 原始命令
            
        Returns:
            str: 合法的类名
        """
        # 移除特殊字符，转换为驼峰命名
        class_name = re.sub(r'[^\w\s]', '', command)
        words = class_name.split()
        return 'TestElla' + ''.join(word.capitalize() for word in words)
    
    def sanitize_method_name(self, command: str) -> str:
        """
        将命令转换为合法的方法名
        
        Args:
            command: 原始命令
            
        Returns:
            str: 合法的方法名
        """
        # 移除特殊字符，替换空格为下划线
        method_name = re.sub(r'[^\w\s-]', '', command.lower())
        method_name = re.sub(r'[-\s]+', '_', method_name)
        return f"test_{method_name}"
    
    def format_expected_text(self, expected_text: Union[str, List[str]]) -> str:
        """
        格式化期望文本为Python列表格式
        
        Args:
            expected_text: 期望文本，可以是字符串或字符串列表
            
        Returns:
            str: 格式化后的Python列表字符串
        """
        if isinstance(expected_text, str):
            return f'["{expected_text}"]'
        elif isinstance(expected_text, list):
            formatted_items = [f'"{item}"' for item in expected_text]
            return f'[{", ".join(formatted_items)}]'
        else:
            return '[""]'
    
    def generate_test_content(self, command: str, expected_text: Union[str, List[str]]) -> str:
        """
        生成测试用例文件内容
        
        Args:
            command: 测试命令
            expected_text: 期望的响应文本
            
        Returns:
            str: 生成的测试文件内容
        """
        class_name = self.sanitize_class_name(command)
        method_name = self.sanitize_method_name(command)
        formatted_expected = self.format_expected_text(expected_text)
        
        template = f'''"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("Ella语音助手基础指令")
class {class_name}(SimpleEllaTest):
    """Ella命令测试类"""
    command = "{command}"
    expected_text = {formatted_expected}

    @allure.title(f"测试{{command}}能正常执行")
    @allure.description(f"{{command}}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        f"""{{self.command}}"""

        command = self.command

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含在期望中"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{{expected_text}}，实际响应: '{{response_text}}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
'''
        return template
    
    def generate_test_file(self, command: str, expected_text: Union[str, List[str]], 
                          output_dir: str = None) -> str:
        """
        生成测试用例文件
        
        Args:
            command: 测试命令
            expected_text: 期望的响应文本
            output_dir: 输出目录，如果为None则使用默认目录
            
        Returns:
            str: 生成的文件路径
        """
        if output_dir is None:
            output_dir = self.base_dir
            
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名和路径
        filename = self.sanitize_filename(command) + ".py"
        filepath = os.path.join(output_dir, filename)
        
        # 生成文件内容
        content = self.generate_test_content(command, expected_text)
        
        # 写入文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        log.info(f"生成测试文件: {filepath}")
        return filepath


def generate_ella_test(command: str, expected_text: Union[str, List[str]], 
                      output_dir: str = "testcases/test_ella") -> str:
    """
    便捷函数：生成Ella测试用例文件
    
    Args:
        command: 测试命令
        expected_text: 期望的响应文本
        output_dir: 输出目录
        
    Returns:
        str: 生成的文件路径
    """
    generator = EllaTestGenerator()
    return generator.generate_test_file(command, expected_text, output_dir)


def create_ella_test_interactive():
    """交互式创建Ella测试用例"""
    print("=== Ella测试用例生成器 ===")

    # 获取命令
    command = input("请输入测试命令: ").strip()
    if not command:
        print("❌ 命令不能为空")
        return

    # 获取期望文本
    print("请输入期望的响应文本（多个文本用回车分隔，输入空行结束）:")
    expected_texts = []
    while True:
        text = input(f"期望文本 {len(expected_texts) + 1}: ").strip()
        if not text:
            break
        expected_texts.append(text)

    if not expected_texts:
        print("❌ 至少需要一个期望文本")
        return

    # 确定期望文本格式
    if len(expected_texts) == 1:
        expected_text = expected_texts[0]
    else:
        expected_text = expected_texts

    try:
        filepath = generate_ella_test(command, expected_text)
        print(f"✅ 测试文件生成成功: {filepath}")

        # 显示生成的文件信息
        generator = EllaTestGenerator()
        class_name = generator.sanitize_class_name(command)
        method_name = generator.sanitize_method_name(command)
        print(f"   类名: {class_name}")
        print(f"   方法名: {method_name}")
        print(f"   文件路径: {filepath}")

    except Exception as e:
        print(f"❌ 生成失败: {e}")


if __name__ == "__main__":
    # import sys
    #
    # if len(sys.argv) == 1:
    #     # 没有参数时启动交互模式
    #     create_ella_test_interactive()
    # elif len(sys.argv) < 3:
    #     print("用法:")
    #     print("  1. 交互模式: python ella_test_generator.py")
    #     print("  2. 命令行模式: python ella_test_generator.py <command> <expected_text1> [expected_text2] ...")
    #     print("示例: python ella_test_generator.py 'open flashlight' 'Flashlight is turned on'")
    #     sys.exit(1)
    # else:
    #     # 命令行模式
    #     command = sys.argv[1]
    #     expected_texts = sys.argv[2:]
    #
    #     if len(expected_texts) == 1:
    #         expected_text = expected_texts[0]
    #     else:
    #         expected_text = expected_texts
    #
    #     try:
    #         filepath = generate_ella_test(command, expected_text)
    #         print(f"✅ 测试文件生成成功: {filepath}")
    #     except Exception as e:
    #         print(f"❌ 生成失败: {e}")
    #         sys.exit(1)
    case_dir = os.path.join(test_ella_dir, "third_coupling")
    command = "navigate to shanghai disneyland"
    expected_text = ["Done",""]
    generate_ella_test(command, expected_text,output_dir=case_dir)
