#!/usr/bin/env python3
"""
Ella测试用例生成工具
快速生成标准化的测试脚本
"""
import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.logger import log


class EllaTestGenerator:
    """Ella测试用例生成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_path = self.project_root / "config" / "status_check_config.json"
        self.output_dir = self.project_root / "testcases" / "test_ella"
        self.supported_apps = self._load_supported_apps()
    
    def _load_supported_apps(self) -> Dict:
        """加载支持的应用配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("status_check_config", {})
        except Exception as e:
            log.warning(f"加载配置失败: {e}")
            return {}
    
    def detect_command_type(self, command: str) -> Tuple[str, bool]:
        """
        检测命令类型
        
        Returns:
            Tuple[str, bool]: (类型, 是否需要状态验证)
        """
        command_lower = command.lower()
        
        # 应用打开类
        if any(keyword in command_lower for keyword in ["open", "打开", "启动"]):
            return "app_open", True
            
        # 系统设置类
        if any(keyword in command_lower for keyword in ["switch to", "设置", "切换到"]):
            return "system_setting", True
            
        # 第三方集成类
        if any(keyword in command_lower for keyword in [
            "navigate", "order", "download", "search", "play", "call"
        ]):
            return "third_party", False
            
        # 默认为第三方集成类
        return "third_party", False
    
    def detect_app_type(self, command: str) -> str:
        """检测应用类型"""
        command_lower = command.lower()
        
        for app_type, config in self.supported_apps.items():
            keywords = config.get("keywords", [])
            if any(keyword.lower() in command_lower for keyword in keywords):
                return app_type
        
        return "unknown"
    
    def generate_class_name(self, command: str) -> str:
        """生成类名"""
        command_type, _ = self.detect_command_type(command)
        
        if command_type == "app_open":
            app_type = self.detect_app_type(command)
            if app_type != "unknown":
                return f"TestEllaOpen{app_type.title()}"
            else:
                # 从命令中提取应用名
                words = command.replace("open", "").replace("打开", "").strip().split()
                app_name = "".join(word.title() for word in words)
                return f"TestEllaOpen{app_name}"
        else:
            # 从命令生成类名
            words = command.replace(" ", "_").replace("to", "").split("_")
            clean_words = [word.title() for word in words if word and word not in ["a", "the", "to"]]
            return f"TestElla{''.join(clean_words)}"
    
    def generate_method_name(self, command: str) -> str:
        """生成方法名"""
        # 将命令转换为方法名
        method_name = command.lower().replace(" ", "_").replace("-", "_")
        # 清理特殊字符
        method_name = "".join(c if c.isalnum() or c == "_" else "_" for c in method_name)
        # 移除连续的下划线
        while "__" in method_name:
            method_name = method_name.replace("__", "_")
        method_name = method_name.strip("_")
        
        return f"test_{method_name}"
    
    def generate_file_name(self, command: str) -> str:
        """生成文件名"""
        method_name = self.generate_method_name(command)
        return f"{method_name}.py"
    
    def suggest_expected_response(self, command: str) -> List[str]:
        """建议期望响应"""
        command_lower = command.lower()
        
        # 根据命令类型建议响应
        if "open" in command_lower or "打开" in command_lower:
            app_type = self.detect_app_type(command)
            if app_type != "unknown":
                app_config = self.supported_apps.get(app_type, {})
                keywords = app_config.get("keywords", [])
                return ["Done"] + keywords[:1]  # Done + 第一个关键词
            return ["Done"]
        
        if "switch to" in command_lower:
            if "performance" in command_lower or "power saving" in command_lower:
                return ["Sorry"]  # 某些模式可能不支持
            return ["Done"]
        
        if any(word in command_lower for word in ["navigate", "order", "download"]):
            return ["Done", ""]
        
        return ["Done"]
    
    def get_verification_method(self, command: str) -> str:
        """获取验证方法"""
        app_type = self.detect_app_type(command)
        if app_type != "unknown":
            app_config = self.supported_apps.get(app_type, {})
            return app_config.get("final_method", app_config.get("initial_method", ""))
        return ""
    
    def generate_app_open_template(self, command: str, class_name: str, method_name: str, 
                                 expected_response: List[str], verification_method: str) -> str:
        """生成应用打开类模板"""
        app_name = self.detect_app_type(command)
        if app_name == "unknown":
            app_name = command.replace("open", "").replace("打开", "").strip()
        
        template = f'''"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class {class_name}(SimpleEllaTest):
    """Ella打开{app_name}测试类"""

    @allure.title("测试{command}")
    @allure.description("测试{command}指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        """测试{command}命令"""
        command = "{command}"
        app_name = '{app_name}'

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = {expected_response}
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{{expected_text}}，实际响应: '{{response_text}}'"

        with allure.step(f"验证{{app_name}}已打开"):
            assert final_status, f"{{app_name}}: 初始={{initial_status}}, 最终={{final_status}}, 响应='{{response_text}}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
'''
        return template
    
    def generate_third_party_template(self, command: str, class_name: str, method_name: str, 
                                    expected_response: List[str]) -> str:
        """生成第三方集成类模板"""
        template = f'''"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class {class_name}(SimpleEllaTest):
    """Ella {command} 测试类"""
    command = "{command}"
    expected_text = {expected_response}

    @allure.title(f"测试{{command}}能正常执行")
    @allure.description(f"{{command}}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        f"""{{self.command}}"""

        command = self.command

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{{expected_text}}，实际响应: '{{response_text}}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
'''
        return template
    
    def generate_test_case(self, command: str, output_path: str = None) -> str:
        """
        生成测试用例
        
        Args:
            command: 要测试的命令
            output_path: 输出路径（可选）
            
        Returns:
            str: 生成的文件路径
        """
        log.info(f"🚀 开始生成测试用例: {command}")
        
        # 检测命令类型
        command_type, need_status_verification = self.detect_command_type(command)
        log.info(f"检测到命令类型: {command_type}, 需要状态验证: {need_status_verification}")
        
        # 生成各种名称
        class_name = self.generate_class_name(command)
        method_name = self.generate_method_name(command)
        file_name = self.generate_file_name(command)
        
        # 建议期望响应
        expected_response = self.suggest_expected_response(command)
        
        # 获取验证方法
        verification_method = self.get_verification_method(command)
        
        log.info(f"类名: {class_name}")
        log.info(f"方法名: {method_name}")
        log.info(f"文件名: {file_name}")
        log.info(f"期望响应: {expected_response}")
        log.info(f"验证方法: {verification_method}")
        
        # 生成代码
        if command_type == "app_open":
            code = self.generate_app_open_template(
                command, class_name, method_name, expected_response, verification_method
            )
            output_dir = self.output_dir / "open_app"
        else:
            code = self.generate_third_party_template(
                command, class_name, method_name, expected_response
            )
            output_dir = self.output_dir / "third_coupling"
        
        # 确定输出路径
        if output_path:
            file_path = Path(output_path)
        else:
            output_dir.mkdir(parents=True, exist_ok=True)
            file_path = output_dir / file_name
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(code)
        
        log.info(f"✅ 测试用例已生成: {file_path}")
        return str(file_path)


def main():
    """主函数 - 交互式生成器"""
    generator = EllaTestGenerator()
    
    print("🚀 Ella测试用例生成工具")
    print("=" * 50)
    
    while True:
        print("\n请输入要测试的命令 (输入 'quit' 退出):")
        command = input("> ").strip()
        
        if command.lower() in ['quit', 'exit', 'q']:
            print("👋 再见!")
            break
        
        if not command:
            print("❌ 请输入有效的命令")
            continue
        
        try:
            # 生成测试用例
            file_path = generator.generate_test_case(command)
            print(f"✅ 测试用例已生成: {file_path}")
            
            # 询问是否继续
            continue_choice = input("\n是否继续生成其他测试用例? (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '']:
                break
                
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
