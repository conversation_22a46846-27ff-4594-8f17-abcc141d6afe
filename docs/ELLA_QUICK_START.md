# Ella测试脚本快速入门 🚀

## 🎯 5分钟快速上手

### 1. 确定指令类型
```
指令包含 "open", "打开" → 应用打开类 → 需要验证状态
指令包含 "navigate", "order", "download" → 第三方集成类 → 只验证响应
指令包含 "switch to", "设置" → 系统设置类 → 需要验证状态
```

### 2. 选择模板并填空

#### 应用打开类模板 (90%的测试用这个)
```python
"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpen{APP_NAME}(SimpleEllaTest):  # 🔧 替换 {APP_NAME}
    """Ella打开{APP_NAME}测试类"""  # 🔧 替换 {APP_NAME}

    @allure.title("测试open {app_name}")  # 🔧 替换 {app_name}
    @allure.description("测试打开{APP_NAME}的指令")  # 🔧 替换 {APP_NAME}
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_{app_name}(self, ella_app):  # 🔧 替换 {app_name}
        """测试open {app_name}命令"""  # 🔧 替换 {app_name}
        command = "open {app_name}"  # 🔧 替换 {app_name}
        app_name = '{app_name}'  # 🔧 替换 {app_name}

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = ["Done"]  # 🔧 根据需要调整
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

#### 第三方集成类模板
```python
"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestElla{FUNCTION_NAME}(SimpleEllaTest):  # 🔧 替换 {FUNCTION_NAME}
    """Ella {FUNCTION_NAME}测试类"""  # 🔧 替换 {FUNCTION_NAME}
    command = "{your_command_here}"  # 🔧 替换完整命令
    expected_text = ["Done", ""]  # 🔧 根据预期调整

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_{function_name}(self, ella_app):  # 🔧 替换 {function_name}
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command, verify_status=False  # 不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

### 3. 常用替换示例

#### 蓝牙指令示例
```python
# 🔧 需要替换的内容
{APP_NAME} → Bluetooth
{app_name} → bluetooth
{your_command_here} → open bluetooth
expected_text → ["Done", "蓝牙"]
```

#### 相机指令示例
```python
# 🔧 需要替换的内容
{APP_NAME} → Camera
{app_name} → camera
{your_command_here} → open camera
expected_text → ["Done", "相机"]
```

#### 导航指令示例
```python
# 🔧 需要替换的内容
{FUNCTION_NAME} → NavigateToDisneyland
{function_name} → navigate_to_disneyland
{your_command_here} → navigate to shanghai disneyland
expected_text → ["Done", ""]
```

## 📋 支持的应用类型速查

### 系统应用 (需要状态验证)
```python
# 蓝牙
command = "open bluetooth"
expected_text = ["Done", "蓝牙"]
验证方法: check_bluetooth_status_smart()

# WiFi  
command = "open wifi"
expected_text = ["Done", "WiFi"]
验证方法: check_wifi_status()

# 手电筒
command = "open flashlight"
expected_text = ["Done", "flashlight"]
验证方法: check_flashlight_status()

# 联系人
command = "open contacts"
expected_text = ["Done", "联系人"]
验证方法: check_contacts_app_opened_smart()

# 相机
command = "open camera"
expected_text = ["Done", "相机"]
验证方法: check_camera_app_opened()

# 天气
command = "open weather"
expected_text = ["Done", "天气"]
验证方法: check_weather_app_opened()

# 设置
command = "open settings"
expected_text = ["Done", "设置"]
验证方法: check_settings_opened()

# 时钟
command = "open clock"
expected_text = ["Done", "时钟"]
验证方法: check_alarm_status()
```

### 第三方应用 (只验证响应)
```python
# Facebook
command = "open facebook"
expected_text = ["Done", "facebook"]
验证方法: check_facebook_app_opened()

# Google地图
command = "navigate to shanghai disneyland"
expected_text = ["Done", ""]
验证方法: check_google_map_app_opened()

# 浏览器
command = "open browser"
expected_text = ["Done", "浏览器"]
验证方法: check_browser_app_opened()
```

### 第三方集成 (只验证响应)
```python
# 订购类
command = "order a burger"
expected_text = ["Done", ""]

# 下载类
command = "download app"
expected_text = ["Done", ""]

# 系统设置类
command = "switch to performance mode"
expected_text = ["Sorry", ""]  # 注意：某些功能可能返回Sorry

# 充电模式类
command = "switch to hyper charge"
expected_text = ["Done", ""]
```

## 🚀 文件命名规范

```python
# 应用打开类
test_open_bluetooth.py
test_open_camera.py
test_open_contacts.py

# 第三方集成类
test_navigate_to_shanghai_disneyland.py
test_order_a_burger.py
test_download_app.py
test_switch_to_performance_mode.py
```

## ⚡ 快速检查清单

### 创建新测试前检查
- [ ] 确定指令类型 (应用打开 vs 第三方集成)
- [ ] 选择正确模板
- [ ] 确定期望响应内容
- [ ] 确定是否需要状态验证

### 代码完成后检查
- [ ] 所有 `{变量}` 都已替换
- [ ] 类名和方法名符合规范
- [ ] 导入语句正确
- [ ] 期望响应设置合理
- [ ] 验证逻辑正确

### 运行测试前检查
- [ ] 设备已连接
- [ ] Ella应用可正常启动
- [ ] 测试环境干净

## 🔧 常见错误修复

### 错误1: 找不到验证方法
```python
# ❌ 错误
assert final_status  # 没有对应的验证方法

# ✅ 正确
# 先确认config/status_check_config.json中有对应配置
# 或者设置 verify_status=False
```

### 错误2: 期望响应不匹配
```python
# ❌ 错误
expected_text = ["Success"]  # 实际返回"Done"

# ✅ 正确
expected_text = ["Done"]  # 根据实际响应调整
```

### 错误3: 类名重复
```python
# ❌ 错误
class TestEllaCommand(SimpleEllaTest):  # 太通用

# ✅ 正确
class TestEllaOpenBluetooth(SimpleEllaTest):  # 具体明确
```

---

## 📚 更多资源
- [完整指南](ELLA_TEST_SCRIPT_GUIDE.md) - 详细的API文档和高级功能
- [配置文件](../config/) - 状态检查和进程清理配置
- [示例代码](../testcases/test_ella/) - 现有测试用例参考
