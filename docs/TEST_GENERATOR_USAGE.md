# Ella测试用例生成工具使用说明 🛠️

## 🎯 工具概述

`tools/test_generator.py` 是一个智能的测试用例生成工具，可以根据Ella指令自动生成标准化的测试脚本，大大提高测试开发效率。

## 🚀 快速使用

### 方法1: 交互式生成
```bash
cd tools
python test_generator.py
```

然后按提示输入命令：
```
🚀 Ella测试用例生成工具
==================================================

请输入要测试的命令 (输入 'quit' 退出):
> open bluetooth

✅ 测试用例已生成: D:\aigc\app_test\testcases\test_ella\open_app\test_open_bluetooth.py

是否继续生成其他测试用例? (y/n): y

请输入要测试的命令 (输入 'quit' 退出):
> navigate to shanghai disneyland

✅ 测试用例已生成: D:\aigc\app_test\testcases\test_ella\third_coupling\test_navigate_to_shanghai_disneyland.py
```

### 方法2: 编程式生成
```python
from tools.test_generator import EllaTestGenerator

generator = EllaTestGenerator()

# 生成单个测试用例
file_path = generator.generate_test_case("open camera")
print(f"生成的文件: {file_path}")

# 批量生成
commands = [
    "open bluetooth",
    "open contacts", 
    "navigate to disneyland",
    "order a burger"
]

for command in commands:
    file_path = generator.generate_test_case(command)
    print(f"✅ {command} -> {file_path}")
```

## 🧠 智能特性

### 1. 自动命令类型检测
工具会自动分析命令并分类：

```python
# 应用打开类 -> 需要状态验证
"open bluetooth" -> app_open (True)
"open camera" -> app_open (True)
"打开联系人" -> app_open (True)

# 第三方集成类 -> 只验证响应
"navigate to disneyland" -> third_party (False)
"order a burger" -> third_party (False)
"download app" -> third_party (False)

# 系统设置类 -> 需要状态验证
"switch to performance mode" -> system_setting (True)
"设置闪光通知" -> system_setting (True)
```

### 2. 智能命名生成
```python
# 输入命令 -> 自动生成的名称
"open bluetooth" -> {
    "class_name": "TestEllaOpenBluetooth",
    "method_name": "test_open_bluetooth", 
    "file_name": "test_open_bluetooth.py"
}

"navigate to shanghai disneyland" -> {
    "class_name": "TestEllaNavigateShanghaiDisneyland",
    "method_name": "test_navigate_to_shanghai_disneyland",
    "file_name": "test_navigate_to_shanghai_disneyland.py"
}
```

### 3. 智能期望响应建议
```python
# 基于命令内容自动建议期望响应
"open bluetooth" -> ["Done", "bluetooth"]
"open camera" -> ["Done", "camera"] 
"navigate to xxx" -> ["Done", ""]
"switch to performance mode" -> ["Sorry"]  # 某些功能可能不支持
```

### 4. 自动文件分类
```python
# 应用打开类 -> testcases/test_ella/open_app/
"open bluetooth" -> testcases/test_ella/open_app/test_open_bluetooth.py

# 第三方集成类 -> testcases/test_ella/third_coupling/  
"navigate to xxx" -> testcases/test_ella/third_coupling/test_navigate_to_xxx.py
```

## 📋 支持的命令模式

### 应用打开类
```python
✅ "open bluetooth"
✅ "open camera" 
✅ "open contacts"
✅ "open weather"
✅ "open flashlight"
✅ "open settings"
✅ "打开蓝牙"
✅ "打开相机"
```

### 第三方集成类
```python
✅ "navigate to shanghai disneyland"
✅ "order a burger"
✅ "download app"
✅ "search for restaurants"
✅ "play music"
✅ "call john"
```

### 系统设置类
```python
✅ "switch to performance mode"
✅ "switch to power saving mode"
✅ "switch to flash notification"
✅ "设置性能模式"
✅ "切换到省电模式"
```

## 🎨 生成的代码特性

### 应用打开类模板特性
- ✅ 完整的状态验证逻辑
- ✅ 智能选择验证方法
- ✅ 标准化的断言消息
- ✅ 完整的Allure报告集成
- ✅ 截图和测试摘要

### 第三方集成类模板特性
- ✅ 跳过状态验证 (`verify_status=False`)
- ✅ 类属性方式定义命令和期望
- ✅ 简化的验证流程
- ✅ 标准化的错误消息

## 🔧 自定义配置

### 1. 添加新的应用类型
编辑 `config/status_check_config.json`:
```json
{
  "status_check_config": {
    "new_app": {
      "keywords": ["new_app", "新应用"],
      "initial_method": "check_new_app_status",
      "final_method": "check_new_app_status_smart",
      "description": "新应用状态"
    }
  }
}
```

### 2. 自定义输出路径
```python
generator = EllaTestGenerator()
file_path = generator.generate_test_case(
    "open calculator", 
    output_path="/custom/path/test_calculator.py"
)
```

## 📊 生成统计示例

```
🧪 测试生成蓝牙指令...
检测到命令类型: app_open, 需要状态验证: True
类名: TestEllaOpenBluetooth
方法名: test_open_bluetooth
文件名: test_open_bluetooth.py
期望响应: ['Done', 'bluetooth']
验证方法: check_bluetooth_status_smart
✅ 测试用例已生成: testcases/test_ella/open_app/test_open_bluetooth.py

🧪 测试生成导航指令...
检测到命令类型: third_party, 需要状态验证: False
类名: TestEllaNavigateShanghaiDisneyland
方法名: test_navigate_to_shanghai_disneyland
文件名: test_navigate_to_shanghai_disneyland.py
期望响应: ['Done', '']
验证方法: check_google_map_app_opened
✅ 测试用例已生成: testcases/test_ella/third_coupling/test_navigate_to_shanghai_disneyland.py
```

## 🎯 最佳实践

### 1. 命令输入规范
```python
✅ 推荐: "open bluetooth"
✅ 推荐: "navigate to shanghai disneyland"
✅ 推荐: "switch to performance mode"

❌ 避免: "bluetooth"  # 不够明确
❌ 避免: "go to disneyland"  # 使用标准动词
```

### 2. 生成后检查
生成代码后建议检查：
- [ ] 期望响应是否合理
- [ ] 验证方法是否正确
- [ ] 类名和方法名是否符合规范
- [ ] 文件路径是否正确

### 3. 手动调整
生成的代码是基础模板，可能需要根据具体需求调整：
- 期望响应内容
- 验证逻辑
- 错误消息
- 测试步骤

## ⚠️ 注意事项

1. **覆盖文件**: 如果目标文件已存在，工具会直接覆盖
2. **配置依赖**: 工具依赖 `config/status_check_config.json` 配置文件
3. **路径要求**: 需要在项目根目录或正确设置Python路径
4. **编码格式**: 生成的文件使用UTF-8编码

## 🔍 故障排除

### 问题1: 找不到配置文件
```
❌ 错误: 加载配置失败: [Errno 2] No such file or directory: 'config/status_check_config.json'

✅ 解决: 确保在项目根目录运行，或检查配置文件是否存在
```

### 问题2: 生成的验证方法不存在
```
❌ 错误: AttributeError: 'EllaDialoguePage' object has no attribute 'check_xxx_status'

✅ 解决: 在页面类中添加对应的验证方法，或修改配置文件
```

### 问题3: 类名冲突
```
❌ 错误: 生成的类名与现有类重复

✅ 解决: 手动修改生成的类名，或删除现有文件
```

---

## 📚 相关文档
- [测试脚本编写指南](ELLA_TEST_SCRIPT_GUIDE.md)
- [快速入门指南](ELLA_QUICK_START.md)
- [配置文件说明](../config/)
