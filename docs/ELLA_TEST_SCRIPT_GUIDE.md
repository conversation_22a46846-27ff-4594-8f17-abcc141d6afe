# Ella测试指令脚本编写指南

## 📋 目录
- [框架概述](#框架概述)
- [测试类型分类](#测试类型分类)
- [基础测试模板](#基础测试模板)
- [验证方法详解](#验证方法详解)
- [配置文件说明](#配置文件说明)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 🏗️ 框架概述

### 核心组件
```
testcases/test_ella/
├── base_ella_test.py          # 基础测试类
├── open_app/                  # 应用打开类指令
├── third_coupling/            # 第三方集成类指令
└── config/
    ├── status_check_config.json    # 状态检查配置
    └── process_cleanup_config.json # 进程清理配置
```

### 测试基类层次
```python
BaseEllaTest                   # 完整功能基类
└── SimpleEllaTest            # 简化版基类（推荐）
```

## 🎯 测试类型分类

### 1. 应用打开类指令 (open_app/)
**特征**: 打开系统应用或第三方应用
**验证点**:
- ✅ 响应内容验证
- ✅ 应用状态验证 
- ✅ 进程检查验证

**支持的应用类型**:
```json
{
  "bluetooth": ["bluetooth", "蓝牙"],
  "contacts": ["contact", "contacts", "联系人", "通讯录", "phone", "dialer"],
  "weather": ["weather", "天气"],
  "camera": ["camera", "photo", "相机", "拍照"],
  "wifi": ["wifi", "wi-fi", "无线网络"],
  "flashlight": ["flashlight", "手电筒", "闪光灯"],
  "clock": ["clock", "alarm", "timer", "时钟", "闹钟", "定时器"],
  "facebook": ["facebook", "fb"],
  "settings": ["settings", "setting", "设置"],
  "music": ["music", "音乐", "播放器"],
  "gallery": ["gallery", "photos", "相册", "图片"],
  "calculator": ["calculator", "计算器"],
  "google_maps": ["disneyland", "迪士尼", "地图", "google maps", "导航"],
  "browser": ["browser", "chrome", "浏览器"]
}
```

### 2. 第三方集成类指令 (third_coupling/)
**特征**: 调用第三方服务或复杂系统功能
**验证点**:
- ✅ 响应内容验证
- ❌ 应用状态验证（通常不适用）
- ❌ 进程检查验证（通常不适用）

**常见类型**:
- 导航指令: `navigate to shanghai disneyland`
- 订购指令: `order a burger`, `order a takeaway`
- 下载指令: `download app`, `download basketball`
- 系统设置: `switch to performance mode`, `switch to flash notification`
- 充电模式: `switch to hyper charge`, `switch to smart charge`

### 3. 系统状态类指令
**特征**: 改变系统设置或状态
**验证点**:
- ✅ 响应内容验证
- ✅ 系统状态验证
- ❌ 应用打开验证（通常不适用）

## 📝 基础测试模板

### 模板1: 应用打开类指令
```python
"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenXXX(SimpleEllaTest):
    """Ella打开XXX应用测试类"""

    @allure.title("测试open xxx")
    @allure.description("测试打开XXX应用的指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_xxx(self, ella_app):
        """测试open xxx命令"""
        command = "open xxx"
        app_name = 'xxx'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = ["Done", "xxx"]  # 根据实际情况调整
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

### 模板2: 第三方集成类指令
```python
"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaXXXCommand(SimpleEllaTest):
    """Ella XXX命令测试类"""
    command = "your command here"
    expected_text = ["Done", ""]  # 根据实际情况调整

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_xxx_command(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

## 🔍 验证方法详解

### 1. 响应内容验证
```python
# 基础验证方法
def verify_expected_in_response(self, expected_text, response_text):
    """
    验证期望内容是否在响应中
    
    Args:
        expected_text: 期望的文本内容，可以是字符串或字符串列表
        response_text: 实际响应文本，可以是字符串或字符串列表
    
    Returns:
        bool: 验证是否通过
    """

# 高级验证方法
def verify_expected_in_response_advanced(self, expected_text, response_text,
                                       search_mode: str = "combined",
                                       match_any: bool = False):
    """
    高级版本的响应验证方法，支持多种搜索模式
    
    Args:
        expected_text: 期望的文本内容
        response_text: 实际响应文本
        search_mode: 搜索模式 ("combined", "individual", "any_match")
        match_any: 是否只需匹配任意一个期望内容
    """
```

### 2. 状态验证方法

#### 系统状态验证
```python
# 蓝牙状态
ella_app.check_bluetooth_status()          # 基础检查
ella_app.check_bluetooth_status_smart()    # 智能检查（推荐）

# WiFi状态
ella_app.check_wifi_status()

# 手电筒状态
ella_app.check_flashlight_status()
```

#### 应用状态验证
```python
# 联系人应用
ella_app.check_contacts_app_opened()       # 基础检查
ella_app.check_contacts_app_opened_smart() # 智能检查（推荐）

# 相机应用
ella_app.check_camera_app_opened()

# 天气应用
ella_app.check_weather_app_opened()

# Facebook应用
ella_app.check_facebook_app_opened()

# 设置应用
ella_app.check_settings_opened()

# 时钟应用
ella_app.check_alarm_status()

# Google地图应用
ella_app.check_google_map_app_opened()

# 浏览器应用
ella_app.check_browser_app_opened()

# 音乐应用
ella_app.check_music_app_opened()

# 相册应用
ella_app.check_gallery_app_opened()

# 计算器应用
ella_app.check_calculator_app_opened()
```

### 3. 智能状态检查机制
框架会根据命令内容自动选择合适的验证方法：

```python
# 自动检测命令类型并选择验证方法
def simple_command_test(self, ella_app, command: str, verify_status: bool = True):
    """
    极简命令测试方法
    
    Args:
        ella_app: Ella应用实例
        command: 要测试的命令
        verify_status: 是否验证状态变化（默认True）
    
    Returns:
        tuple: (初始状态, 最终状态, 响应文本)
    """
```

## ⚙️ 配置文件说明

### 状态检查配置 (config/status_check_config.json)
```json
{
  "status_check_config": {
    "bluetooth": {
      "keywords": ["bluetooth", "蓝牙"],
      "initial_method": "check_bluetooth_status",
      "final_method": "check_bluetooth_status_smart",
      "description": "蓝牙状态"
    }
  }
}
```

### 进程清理配置 (config/process_cleanup_config.json)
```json
{
  "process_cleanup_config": {
    "common_user_apps": [
      {
        "package": "com.transsion.aivoiceassistant",
        "description": "Ella语音助手",
        "category": "system_app"
      }
    ],
    "cleanup_settings": {
      "gentle_cleanup_enabled": true,
      "recent_apps_fallback_enabled": true,
      "min_apps_for_fallback": 5
    }
  }
}
```

## 💡 最佳实践

### 1. 命名规范
```python
# 文件命名
test_open_bluetooth.py          # 应用打开类
test_navigate_to_disneyland.py  # 第三方集成类

# 类命名
class TestEllaOpenBluetooth(SimpleEllaTest):
class TestEllaNavigateToDisneyland(SimpleEllaTest):

# 方法命名
def test_open_bluetooth(self, ella_app):
def test_navigate_to_disneyland(self, ella_app):
```

### 2. 期望响应设置
```python
# 成功响应
expected_text = ["Done", "已打开", "成功"]

# 失败响应
expected_text = ["Sorry", "无法", "失败"]

# 特定功能响应
expected_text = ["bluetooth", "蓝牙"]  # 功能相关关键词
```

### 3. 验证策略选择

#### 应用打开类指令
```python
# 必须验证
✅ 响应内容验证
✅ 应用状态验证
✅ 截图记录

# 示例
with allure.step("验证响应包含Done"):
    expected_text = ["Done"]
    result = self.verify_expected_in_response(expected_text, response_text)
    assert result

with allure.step("验证蓝牙已打开"):
    assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"
```

#### 第三方集成类指令
```python
# 必须验证
✅ 响应内容验证
✅ 截图记录

# 可选验证
❓ 应用状态验证（根据具体功能决定）

# 示例
with allure.step(f"执行命令: {command}"):
    initial_status, final_status, response_text = self.simple_command_test(
        ella_app, command, verify_status=False  # 不验证状态
    )
```

### 4. 错误处理
```python
# 响应验证失败
assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

# 状态验证失败
assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

# 自定义验证失败
assert custom_check(), f"自定义验证失败: 详细信息"
```

## ❓ 常见问题

### Q1: 如何判断指令类型？
**A**: 根据指令功能分类：
- 包含"open"、"打开"等关键词 → 应用打开类
- 包含"navigate"、"order"、"download"等 → 第三方集成类
- 包含"switch to"、"设置"等 → 系统设置类

### Q2: 什么时候需要验证状态？
**A**: 
- ✅ 应用打开类指令：必须验证
- ❌ 第三方集成类指令：通常不验证
- ✅ 系统设置类指令：必须验证

### Q3: 如何选择期望响应？
**A**:
- 成功指令：`["Done", "已完成", "成功"]`
- 失败指令：`["Sorry", "无法", "失败"]`
- 特定功能：包含功能相关关键词

### Q4: 智能检查和基础检查的区别？
**A**:
- **基础检查**: 直接检查状态，速度快
- **智能检查**: 包含进程检查、自动返回等，更可靠（推荐）

### Q5: 如何添加新的状态检查？
**A**:
```python
# 1. 在配置文件中添加
# 2. 或使用代码动态添加
self.add_custom_status_check(
    command_type="new_app",
    keywords=["new", "新应用"],
    initial_method="check_new_app_status",
    final_method="check_new_app_status_smart"
)
```

---

## 📚 参考资源
- [BaseEllaTest API文档](testcases/test_ella/base_ella_test.py)
- [配置文件示例](config/)
- [测试用例示例](testcases/test_ella/)
